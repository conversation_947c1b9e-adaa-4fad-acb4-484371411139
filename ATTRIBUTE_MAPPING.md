# Sistema de Mapeamento por Atributos - OpenAI Integration

## 📋 Visão Geral

O sistema OpenAI agora utiliza os atributos `ativo-product` e `ativo-category` dos elementos HTML para fazer um mapeamento mais preciso e inteligente das alocações sugeridas pela IA.

## 🏷️ Atributos Identificados

### Estrutura HTML Atual

```html
<div ativo-product="[PRODUTO]" ativo-category="[CATEGORIA]" class="patrimonio_interactive_item">
  <!-- conteúdo do item -->
</div>
```

### Mapeamento Encontrado

| Índice | ativo-category | ativo-product | Descrição |
|--------|----------------|---------------|-----------|
| 0 | Renda Fixa | CDB | CDB, LCI, LCA |
| 1 | Renda Fixa | CRI | CRI, CRA, Debêntures |
| 2 | Renda Fixa | Títulos Públicos | Tesouro Direto |
| 3 | Fundo de Investimento | Ações | Fundos de Ações |
| 4 | Fundo de Investimento | (sem atributo) | Fundos Liquidez |
| 5 | Fundo de Investimento | Renda Fixa | Fundos Renda Fixa |
| 6 | Renda Variável | (sem atributo) | Ações |
| 7 | Renda Variável | (sem atributo) | Estruturada |
| 8 | Renda Variável | (sem atributo) | Outro tipo |
| 9-14 | Outros | (sem atributo) | Outros investimentos |

## 🔍 Sistema de Scoring

### Algoritmo de Correspondência

O sistema usa um algoritmo de scoring para encontrar a melhor correspondência:

```javascript
calculateMatchScore(searchTerm, item) {
  let score = 0;
  
  // Correspondências exatas
  if (product === searchTerm) score += 100;
  if (category === searchTerm) score += 80;
  
  // Correspondências parciais no produto
  if (product.includes(searchTerm)) score += 60;
  if (searchTerm.includes(product)) score += 50;
  
  // Correspondências parciais na categoria
  if (category.includes(searchTerm)) score += 40;
  if (searchTerm.includes(category)) score += 30;
  
  // Correspondências específicas
  // ... lógica adicional
  
  return score;
}
```

### Prioridades de Matching

1. **Correspondência exata no produto** (Score: 100)
2. **Correspondência exata na categoria** (Score: 80)
3. **Correspondências específicas** (Score: 70)
4. **Produto contém termo** (Score: 60)
5. **Termo contém produto** (Score: 50)
6. **Categoria contém termo** (Score: 40)
7. **Termo contém categoria** (Score: 30)

## 🎯 Exemplos de Mapeamento

### Prompts e Correspondências

| Prompt do Usuário | Termo Extraído | Melhor Match | Índice | Justificativa |
|-------------------|----------------|--------------|--------|---------------|
| "50% em CDB" | "cdb" | CDB | 0 | Correspondência específica |
| "30% em ações" | "ações" | Ações (Fundo) | 3 | Score mais alto em fundos |
| "20% títulos públicos" | "títulos públicos" | Títulos Públicos | 2 | Correspondência exata |
| "25% renda fixa" | "renda fixa" | CDB | 0 | Primeira categoria renda fixa |
| "40% fundos" | "fundos" | Ações (Fundo) | 3 | Primeiro fundo disponível |

### Casos Especiais

```javascript
// Correspondências específicas configuradas
const specificMatches = {
  'cdb': ['cdb'],
  'lci': ['cdb'],           // LCI mapeia para CDB
  'lca': ['cdb'],           // LCA mapeia para CDB
  'tesouro': ['títulos públicos'],
  'cri': ['cri'],
  'cra': ['cri'],           // CRA mapeia para CRI
  'debenture': ['cri'],     // Debênture mapeia para CRI
  'ações': ['ações'],
  'estruturada': ['estruturada']
};
```

## 🔧 Funções de Debug

### Comandos Disponíveis no Console

```javascript
// Visualizar mapeamento completo
debugOpenAI.mapping()

// Testar correspondência de termo
debugOpenAI.test('cdb')
debugOpenAI.test('renda fixa')
debugOpenAI.test('ações')

// Obter lista de itens
debugOpenAI.items()

// Testar extração de texto
debugOpenAI.extract('50% CDB, 30% ações, 20% títulos públicos')

// Encontrar melhor correspondência
debugOpenAI.findMatch('tesouro')
```

### Exemplo de Output Debug

```
=== MAPEAMENTO DE ITENS PATRIMONIAIS ===
[0] Renda Fixa - CDB
    Container: <div ativo-product="CDB" ativo-category="Renda Fixa"...>
    Atributos: { ativo-product: "CDB", ativo-category: "Renda Fixa" }
---
[1] Renda Fixa - CRI
    Container: <div ativo-product="CRI" ativo-category="Renda Fixa"...>
    Atributos: { ativo-product: "CRI", ativo-category: "Renda Fixa" }
---
=== FIM DO MAPEAMENTO ===
```

## 📈 Melhorias vs Sistema Anterior

### Sistema Anterior (Baseado em Índices)
- Mapeamento fixo por posição
- Menos flexível para mudanças
- Dependente da ordem dos elementos

### Sistema Atual (Baseado em Atributos)
- ✅ Mapeamento semântico inteligente
- ✅ Tolerante a mudanças de ordem
- ✅ Sistema de scoring flexível
- ✅ Fallback para sistema anterior
- ✅ Debug tools integradas

## 🚀 Prompts Otimizados para IA

### Termos Recomendados

A IA foi instruída a usar termos específicos que mapeiam melhor:

```
TERMOS EXATOS A USAR:
- "CDB" (não "CDB/LCI/LCA")
- "CRI" (não "CRI/CRA")
- "Títulos Públicos" (não "Tesouro Direto")
- "Ações" (especificar se fundo ou renda variável)
- "Estruturada"
- "Renda Fixa" (para fundos)
```

### Formato de Resposta Estruturada

```
**ALOCAÇÃO SUGERIDA:**
• Renda Fixa: 60% (R$ 600.000)
• Fundos de Investimento: 25% (R$ 250.000)
• Renda Variável: 15% (R$ 150.000)

**DETALHAMENTO POR CATEGORIA:**
1. **Renda Fixa (60%):**
   - CDB: 30%
   - CRI: 20%
   - Títulos Públicos: 10%

2. **Fundos de Investimento (25%):**
   - Ações: 15%
   - Renda Fixa: 10%

3. **Renda Variável (15%):**
   - Ações: 10%
   - Estruturada: 5%
```

## 🔮 Próximas Melhorias

### Funcionalidades Planejadas

1. **Auto-discovery de atributos**: Detectar automaticamente novos atributos
2. **Machine Learning**: Aprender com interações do usuário
3. **Validação cruzada**: Verificar consistência entre atributos e conteúdo
4. **Métricas de precisão**: Medir eficácia do mapeamento
5. **Interface de configuração**: Permitir ajustes pelo usuário

### Expansão de Atributos

```html
<!-- Futuras expansões -->
<div 
  ativo-product="CDB" 
  ativo-category="Renda Fixa"
  ativo-risk="baixo"
  ativo-liquidity="alta"
  ativo-tax="isento"
  class="patrimonio_interactive_item">
```

## 📊 Monitoramento

### Logs de Debug

- Scores de correspondência
- Termos não mapeados
- Fallbacks utilizados
- Performance do algoritmo

### Métricas Importantes

- Taxa de correspondência bem-sucedida
- Uso de fallbacks
- Tempo de processamento
- Satisfação do usuário

---

*Sistema implementado para melhorar a precisão e flexibilidade do mapeamento de alocações patrimoniais via IA.*