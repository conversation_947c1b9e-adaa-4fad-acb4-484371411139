/**
 * Chart Animation System
 * Handles GSAP-based chart animations synchronized with range sliders
 */
class ChartAnimationSystem {
  constructor() {
    this.isInitialized = false;
    this.timeline = null;
  }

  init() {
    if (this.isInitialized) {
      return;
    }

    document.addEventListener('DOMContentLoaded', () => {
      this.waitForGSAP();
    });

    this.isInitialized = true;
  }

  waitForGSAP() {
    if (window.gsap) {
      this.initializeChartSystem();
    } else {
      setTimeout(() => this.waitForGSAP(), 50);
    }
  }

  initializeChartSystem() {
    // Use jQuery if available, otherwise use vanilla JS
    if (window.$) {
      this.initializeWithJQuery();
    } else {
      this.initializeVanilla();
    }
  }

  initializeWithJQuery() {
    const { $ } = window;

    // Inicializa o gráfico único
    $('.chart_wrap').each((index, element) => {
      this.timeline = window.gsap.timeline({
        paused: true,
      });

      $(element)
        .find('.chart_column')
        .each((columnIndex, columnElement) => {
          let bar = $(columnElement).find('.chart_bar');
          let start = +$(columnElement).attr('start');
          let end = +$(columnElement).attr('end');
          let numberText = $(columnElement).find('.chart_number');
          let number = { value: start };

          this.timeline
            .fromTo(
              bar,
              {
                height: start + '%',
              },
              {
                height: end + '%',
              },
              '<'
            )
            .fromTo(
              number,
              {
                value: start,
              },
              {
                value: end,
                onUpdate: () => numberText.text(Math.round(number.value)),
              },
              '<'
            );
        });
    });

    // Conecta TODOS os sliders ao mesmo gráfico
    $('range-slider.slider').each((index, element) => {
      // CORRIGE O STEP PROBLEMÁTICO
      element.setAttribute('step', '0.01'); // Muda de 0.00000001 para 0.01

      element.addEventListener('input', () => {
        if (this.timeline) {
          this.timeline.progress(element.value);
        }
      });
    });
  }

  initializeVanilla() {
    // Vanilla JavaScript implementation
    const chartWraps = document.querySelectorAll('.chart_wrap');

    chartWraps.forEach((chartWrap) => {
      this.timeline = window.gsap.timeline({
        paused: true,
      });

      const chartColumns = chartWrap.querySelectorAll('.chart_column');

      chartColumns.forEach((column) => {
        const bar = column.querySelector('.chart_bar');
        const start = parseInt(column.getAttribute('start')) || 0;
        const end = parseInt(column.getAttribute('end')) || 0;
        const numberText = column.querySelector('.chart_number');
        const number = { value: start };

        if (bar && numberText) {
          this.timeline
            .fromTo(
              bar,
              {
                height: start + '%',
              },
              {
                height: end + '%',
              },
              '<'
            )
            .fromTo(
              number,
              {
                value: start,
              },
              {
                value: end,
                onUpdate: () => {
                  numberText.textContent = Math.round(number.value);
                },
              },
              '<'
            );
        }
      });
    });

    // Conecta TODOS os sliders ao mesmo gráfico
    const sliders = document.querySelectorAll('range-slider.slider');

    sliders.forEach((slider) => {
      // CORRIGE O STEP PROBLEMÁTICO
      slider.setAttribute('step', '0.01'); // Muda de 0.00000001 para 0.01

      slider.addEventListener('input', () => {
        if (this.timeline) {
          this.timeline.progress(slider.value);
        }
      });
    });
  }

  // Public API methods
  playChart() {
    if (this.timeline) {
      this.timeline.play();
    }
  }

  pauseChart() {
    if (this.timeline) {
      this.timeline.pause();
    }
  }

  resetChart() {
    if (this.timeline) {
      this.timeline.progress(0);
    }
  }

  setProgress(progress) {
    if (this.timeline && progress >= 0 && progress <= 1) {
      this.timeline.progress(progress);
    }
  }

  getProgress() {
    return this.timeline ? this.timeline.progress() : 0;
  }

  isPlaying() {
    return this.timeline ? this.timeline.isActive() : false;
  }

  getDuration() {
    return this.timeline ? this.timeline.duration() : 0;
  }

  // Advanced animation controls
  seekTo(time) {
    if (this.timeline && time >= 0) {
      this.timeline.seek(time);
    }
  }

  setTimeScale(scale) {
    if (this.timeline && scale > 0) {
      this.timeline.timeScale(scale);
    }
  }

  reverse() {
    if (this.timeline) {
      this.timeline.reverse();
    }
  }

  restart() {
    if (this.timeline) {
      this.timeline.restart();
    }
  }

  // Event handlers for external control
  onComplete(callback) {
    if (this.timeline && typeof callback === 'function') {
      this.timeline.eventCallback('onComplete', callback);
    }
  }

  onUpdate(callback) {
    if (this.timeline && typeof callback === 'function') {
      this.timeline.eventCallback('onUpdate', callback);
    }
  }

  onStart(callback) {
    if (this.timeline && typeof callback === 'function') {
      this.timeline.eventCallback('onStart', callback);
    }
  }

  // Cleanup
  destroy() {
    if (this.timeline) {
      this.timeline.kill();
      this.timeline = null;
    }
    this.isInitialized = false;
  }
}

// Export for use in other modules
export { ChartAnimationSystem };
