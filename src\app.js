/**
 * Main Application
 * Integrates all extracted JavaScript modules and initializes them
 */
import { ChartAnimationSystem } from './modules/chart-animation.js';
import { CurrencyControlSystem } from './modules/currency-control.js';
import { CurrencyFormattingSystem } from './modules/currency-formatting.js';
import { eventCoordinator } from './modules/event-coordinator.js';
import { MotionAnimationSystem } from './modules/motion-animation.js';
import { OpenAIAllocationSystem } from './modules/openai-allocation.js';
import { PatrimonySyncSystem } from './modules/patrimony-sync.js';
import { ProductSystem } from './modules/product-system.js';
import { SectionVisibilitySystem } from './modules/section-visibility.js';

/**
 * Main Application Class
 * Coordinates initialization and management of all systems
 */
class ReinoCalculatorApp {
  constructor() {
    // Initialize EventCoordinator first
    this.eventCoordinator = eventCoordinator;
    this.systems = {
      currencyControl: new CurrencyControlSystem(),
      currencyFormatting: new CurrencyFormattingSystem(),
      motionAnimation: new MotionAnimationSystem(),
      productSystem: new ProductSystem(),
      openaiAllocation: new OpenAIAllocationSystem(),
      sectionVisibility: new SectionVisibilitySystem(),
      patrimonySync: new PatrimonySyncSystem(),
      chartAnimation: new ChartAnimationSystem(),
    };

    this.isInitialized = false;
    this.initializationOrder = [
      'currencyFormatting', // Base currency system first
      'patrimonySync', // Core patrimony synchronization BEFORE currency controls
      'currencyControl', // Currency controls AFTER patrimony sync
      'chartAnimation', // Chart animations
      'motionAnimation', // Motion effects
      'productSystem', // Product interactions
      'sectionVisibility', // Section visibility control
      'openaiAllocation', // AI allocation (last, optional)
    ];
  }

  /**
   * Initialize all systems in the correct order
   */
  async init() {
    if (this.isInitialized) {
      return;
    }

    try {
      // Initialize systems in order
      for (const systemName of this.initializationOrder) {
        const system = this.systems[systemName];
        if (system && typeof system.init === 'function') {
          await system.init();
        }
      }

      this.isInitialized = true;
      this.setupGlobalAPI();
      this.setupErrorHandling();

      // Dispatch ready event
      document.dispatchEvent(
        new CustomEvent('reinoCalculatorReady', {
          detail: {
            app: this,
            systems: this.systems,
          },
        })
      );
    } catch (error) {
      // Silent error handling
    }
  }

  /**
   * Setup global API for debugging and external access
   */
  setupGlobalAPI() {
    window.ReinoCalculator = {
      app: this,
      systems: this.systems,

      // Utility methods
      restart: () => this.restart(),
      getSystemStatus: () => this.getSystemStatus(),

      // System controls
      currency: {
        control: this.systems.currencyControl,
        formatting: this.systems.currencyFormatting,
      },

      animation: {
        motion: this.systems.motionAnimation,
        chart: this.systems.chartAnimation,
      },

      ui: {
        products: this.systems.productSystem,
        visibility: this.systems.sectionVisibility,
      },

      data: {
        patrimony: this.systems.patrimonySync,
        ai: this.systems.openaiAllocation,
      },
    };
  }

  /**
   * Setup global error handling
   */
  setupErrorHandling() {
    window.addEventListener('error', (event) => {
      // console.error('Reino Calculator Error:', event.error);
    });

    window.addEventListener('unhandledrejection', (event) => {
      // console.error('Reino Calculator Unhandled Promise Rejection:', event.reason);
    });
  }

  /**
   * Get status of all systems
   */
  getSystemStatus() {
    const status = {};

    for (const [name, system] of Object.entries(this.systems)) {
      status[name] = {
        initialized: system.isInitialized || false,
        available: typeof system.init === 'function',
      };
    }

    return status;
  }

  /**
   * Restart the application
   */
  async restart() {
    // console.log('Restarting Reino Calculator App...');

    // Cleanup existing systems
    for (const system of Object.values(this.systems)) {
      if (typeof system.cleanup === 'function') {
        system.cleanup();
      }
    }

    this.isInitialized = false;

    // Reinitialize
    await this.init();
  }

  /**
   * Cleanup all systems
   */
  cleanup() {
    for (const system of Object.values(this.systems)) {
      if (typeof system.cleanup === 'function') {
        system.cleanup();
      }
    }

    this.isInitialized = false;
    delete window.ReinoCalculator;

    // console.log('Reino Calculator App cleaned up');
  }
}

// Create and initialize the application
const app = new ReinoCalculatorApp();

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => app.init());
} else {
  app.init();
}

// Cleanup on page unload
window.addEventListener('beforeunload', () => app.cleanup());

// Export for manual control if needed
export { ReinoCalculatorApp };
