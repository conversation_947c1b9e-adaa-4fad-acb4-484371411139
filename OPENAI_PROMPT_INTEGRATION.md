# Integração OpenAI - Prompt Personalizado

Este documento descreve a nova funcionalidade de prompt personalizado integrada ao sistema de alocação de patrimônio.

## 📋 Visão Geral

A funcionalidade permite que usuários digitem instruções personalizadas em linguagem natural para alocar seu patrimônio, e o sistema automaticamente aplica as sugestões da IA aos inputs individuais.

## 🎯 Componentes Integrados

### HTML Elements
- **Input**: `.prompt-input` - Campo de texto para inserir instruções
- **Botão**: `.process-prompt` - Bot<PERSON> "Alocar" que processa o prompt
- **Container**: `.ia-input_wrapper` - Wrapper do sistema de IA

### Funcionalidades

1. **Entrada de Prompt**: Campo de texto com placeholder "Escreva como quer dividir seu patrimônio"
2. **Processamento**: Botão que envia o prompt para OpenAI e processa a resposta
3. **Aplicação Automática**: Atualiza os valores nos inputs patrimoniais automaticamente
4. **Feedback Visual**: Animações e notificações durante o processo

## 🚀 Como Usar

### Para o Usuário Final

1. **Digite o valor do patrimônio** no input principal
2. **Escreva sua instrução** no campo de prompt, exemplos:
   - "Quero 60% em renda fixa e 40% em renda variável"
   - "Distribua 50% em CDB, 30% em ações e 20% em fundos"
   - "Perfil conservador com foco em liquidez"
3. **Clique em "Alocar"** ou pressione Enter
4. **Aguarde o processamento** (indicador de loading aparece)
5. **Visualize o resultado** aplicado automaticamente nos inputs

### Estados Visuais

- **Loading**: Input e botão ficam desabilitados com opacidade reduzida
- **Processando**: Botão mostra "Processando..." 
- **Sucesso**: Toast verde com confirmação
- **Erro**: Toast vermelho com mensagem de erro
- **Atualizando**: Items patrimoniais mostram animação de highlight

## 🔧 Implementação Técnica

### Fluxo de Processamento

```javascript
// 1. Usuário digita prompt e clica no botão
processPrompt() → 
// 2. Valida entrada e valor do patrimônio
validateInput() →
// 3. Envia para OpenAI com prompt estruturado
callOpenAIWithCustomPrompt() →
// 4. Extrai percentuais da resposta
extractAllocationsFromText() →
// 5. Mapeia categorias para índices dos inputs
mapCategoryToIndex() →
// 6. Aplica valores com animação
updatePatrimonyItems() →
// 7. Sincroniza com outros sistemas
triggerPatrimonyUpdate()
```

### Extração de Percentuais

O sistema usa regex patterns para identificar alocações:

```javascript
// Padrões suportados:
"Renda Fixa: 60%"
"30% para ações"  
"• CDB: 25%"
"Fundos de investimento 40%"
```

### Mapeamento de Categorias

```javascript
const categoryMap = {
  // Renda Fixa (índices 0-2)
  'renda fixa': 0,
  'cdb': 0, 'lci': 0, 'lca': 0,
  'tesouro': 1,
  'cri': 2, 'cra': 2, 'debenture': 2,

  // Fundos (índices 3-5)  
  'fundo': 3, 'multimercado': 3,
  'fundo de ações': 4,
  'fundo imobiliário': 5,

  // Renda Variável (índices 6-8)
  'ações': 6,
  'fiis': 7,
  'etf': 8,

  // Outros (índices 9-11)
  'outros': 9,
  'criptomoedas': 10,
  'commodities': 11
};
```

## 📝 Prompt Engineering

### Prompt Estruturado Enviado à IA

```
O investidor possui um patrimônio total de R$ X e fez a seguinte solicitação:

"[PROMPT DO USUÁRIO]"

Com base nesta solicitação, por favor forneça uma resposta estruturada seguindo EXATAMENTE este formato:

**ALOCAÇÃO SUGERIDA:**
• Renda Fixa: X% (R$ valor)
• Fundos de Investimento: Y% (R$ valor)  
• Renda Variável: Z% (R$ valor)
• Outros Investimentos: W% (R$ valor)

**DETALHAMENTO POR CATEGORIA:**
[...]

IMPORTANTE: Use SEMPRE percentuais numéricos precisos que somem 100%.
```

## 🎨 Estilos e Animações

### Classes CSS Adicionadas

```css
.ai-updating {
  background: linear-gradient(90deg, rgba(76, 175, 80, 0.1), rgba(76, 175, 80, 0.2), rgba(76, 175, 80, 0.1));
  background-size: 200% 100%;
  animation: aiUpdatePulse 1s ease-in-out;
  border: 2px solid rgba(76, 175, 80, 0.3);
  border-radius: 8px;
}

@keyframes aiUpdatePulse {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}
```

### Toast Notifications

- **Sucesso**: Verde com ícone ✅
- **Warning**: Laranja com ícone ⚠️  
- **Erro**: Vermelho com ícone ❌
- **Animação**: Slide in/out pela direita
- **Duração**: 3s (sucesso), 5s (erro)

## 🔄 Integração com Outros Módulos

### PatrimonySync
- Dispara eventos de sincronização após aplicação
- Atualiza totais e percentuais automaticamente
- Mantém cache das alocações

### ProductSystem  
- Inputs ficam interativos após aplicação
- Animações de hover e focus funcionam normalmente
- Pin function continua operacional

### CurrencyFormatting
- Valores são formatados corretamente
- Validação de entrada mantida
- Sincronização bidirecional preservada

## 🛡️ Tratamento de Erros

### Cenários de Erro

1. **Prompt vazio**: Warning toast
2. **Valor patrimônio zero**: Console warning
3. **API OpenAI falha**: Error toast com detalhes
4. **Resposta sem percentuais**: Warning toast
5. **Erro de parsing**: Fallback para padrões simples

### Fallbacks

- Se extração estruturada falha, tenta padrões gerais
- Se categoria não mapeada, usa renda fixa como default
- Se percentuais não somam 100%, normaliza proporcionalmente

## 🔐 Configuração da API

### Chave da API OpenAI

```javascript
// Configuração atual (desenvolvimento)
getAPIKey() {
  return window.process?.env?.OPENAI_API_KEY || 'sua-chave-aqui';
}

// Configuração recomendada (produção)
getAPIKey() {
  return fetch('/api/openai-key')
    .then(res => res.json())
    .then(data => data.key);
}
```

### Modelos Suportados

- **Atual**: GPT-4
- **Alternativas**: GPT-3.5-turbo, GPT-4-turbo
- **Tokens**: Máximo 1200 para resposta
- **Temperature**: 0.7 para criatividade balanceada

## 📊 Métricas e Monitoramento

### Eventos Customizados

```javascript
// Disparado quando alocação é aplicada
document.addEventListener('aiAllocationUpdate', (event) => {
  console.log('Alocação aplicada:', event.detail);
});
```

### Logs

- Erros de API são logados no console
- Warnings para validações falhas
- Sucesso silencioso (apenas toast visual)

## 🚀 Próximos Passos

### Melhorias Planejadas

1. **Cache de respostas**: Evitar chamadas desnecessárias
2. **Templates de prompt**: Prompts pré-definidos para perfis comuns
3. **Histórico**: Salvar alocações anteriores  
4. **Validação avançada**: Verificar coerência das sugestões
5. **A/B Testing**: Diferentes estratégias de prompt

### Integrações Futuras

- **Assistente por voz**: Entrada via reconhecimento de voz
- **Chat interface**: Conversa contínua com a IA
- **Análise de sentimento**: Detectar perfil de risco do texto
- **Backtesting**: Simular performance das alocações sugeridas

## 📞 Suporte

Para dúvidas ou problemas com a integração OpenAI:

1. Verifique se a chave da API está configurada
2. Confirme se o valor do patrimônio foi inserido  
3. Teste com prompts simples primeiro
4. Verifique console do navegador para erros
5. Entre em contato com a equipe de desenvolvimento

---

*Documentação atualizada em: $(new Date().toLocaleDateString('pt-BR'))*