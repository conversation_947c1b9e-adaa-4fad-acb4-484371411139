/**
 * Section Visibility System
 * Controls showing/hiding of different sections based on user interactions
 */
export class SectionVisibilitySystem {
  constructor() {
    this.isInitialized = false;
    this.currentSection = 'calculadora';
    this.sections = new Map();
    this.triggers = new Map();
    this.Motion = null;
  }

  init() {
    if (this.isInitialized) {
      return;
    }

    document.addEventListener('DOMContentLoaded', () => {
      this.waitForMotion();
    });

    this.isInitialized = true;
  }

  waitForMotion() {
    if (window.Motion) {
      this.Motion = window.Motion;
      this.initSystem();
    } else {
      setTimeout(() => this.waitForMotion(), 50);
    }
  }

  initSystem() {
    this.initializeSections();
    this.initializeTriggers();
    this.setupEventListeners();
    this.showInitialSection();
  }

  initializeSections() {
    // Mapeamento das seções principais
    const sectionSelectors = {
      calculadora: '.section_calculadora',
      resultado: '.section_resultado',
      alocacao: '.section_alocacao',
      detalhes: '.section_detalhes',
      configuracoes: '.section_configuracoes',
    };

    Object.entries(sectionSelectors).forEach(([key, selector]) => {
      const element = document.querySelector(selector);
      if (element) {
        this.sections.set(key, {
          element,
          selector,
          visible: false,
          animating: false,
        });
      }
    });
  }

  initializeTriggers() {
    // Mapeamento dos elementos que acionam mudanças de seção
    const triggerSelectors = {
      'show-calculadora': '[data-show="calculadora"]',
      'show-resultado': '[data-show="resultado"]',
      'show-alocacao': '[data-show="alocacao"]',
      'show-detalhes': '[data-show="detalhes"]',
      'show-configuracoes': '[data-show="configuracoes"]',
      'toggle-calculadora': '[data-toggle="calculadora"]',
      'toggle-resultado': '[data-toggle="resultado"]',
    };

    Object.entries(triggerSelectors).forEach(([key, selector]) => {
      const elements = document.querySelectorAll(selector);
      if (elements.length > 0) {
        this.triggers.set(key, Array.from(elements));
      }
    });
  }

  setupEventListeners() {
    // Event listeners para mostrar seções
    this.triggers.forEach((elements, key) => {
      if (key.startsWith('show-')) {
        const sectionName = key.replace('show-', '');
        elements.forEach((element) => {
          element.addEventListener('click', (e) => {
            e.preventDefault();
            this.showSection(sectionName);
          });
        });
      }
    });

    // Event listeners para toggle de seções
    this.triggers.forEach((elements, key) => {
      if (key.startsWith('toggle-')) {
        const sectionName = key.replace('toggle-', '');
        elements.forEach((element) => {
          element.addEventListener('click', (e) => {
            e.preventDefault();
            this.toggleSection(sectionName);
          });
        });
      }
    });

    // Listener para teclas de atalho
    document.addEventListener('keydown', (e) => {
      this.handleKeyboardShortcuts(e);
    });

    // Listener para mudanças de hash na URL
    window.addEventListener('hashchange', () => {
      this.handleHashChange();
    });
  }

  showInitialSection() {
    // Verifica se há uma seção especificada na URL
    const hash = window.location.hash.replace('#', '');
    const initialSection = hash && this.sections.has(hash) ? hash : 'calculadora';

    this.showSection(initialSection, false); // false = sem animação inicial
  }

  async showSection(sectionName, animate = true) {
    if (!this.sections.has(sectionName)) {
      return;
    }

    const targetSection = this.sections.get(sectionName);

    if (targetSection.visible && this.currentSection === sectionName) {
      return; // Seção já está visível
    }

    // Esconde seção atual
    if (this.currentSection && this.currentSection !== sectionName) {
      await this.hideSection(this.currentSection, animate);
    }

    // Mostra nova seção
    await this.displaySection(sectionName, animate);

    this.currentSection = sectionName;
    this.updateURL(sectionName);
    this.updateActiveStates(sectionName);
  }

  async hideSection(sectionName, animate = true) {
    if (!this.sections.has(sectionName)) {
      return;
    }

    const section = this.sections.get(sectionName);

    if (!section.visible || section.animating) {
      return;
    }

    section.animating = true;

    if (animate && this.Motion) {
      await this.Motion.animate(
        section.element,
        {
          opacity: [1, 0],
          y: [0, -30],
          scale: [1, 0.95],
        },
        {
          duration: 0.3,
          ease: 'circIn',
        }
      ).finished;
    }

    section.element.style.display = 'none';
    section.visible = false;
    section.animating = false;
  }

  async displaySection(sectionName, animate = true) {
    if (!this.sections.has(sectionName)) {
      return;
    }

    const section = this.sections.get(sectionName);

    if (section.visible || section.animating) {
      return;
    }

    section.animating = true;
    section.element.style.display = 'block';

    if (animate && this.Motion) {
      // Prepara elemento para animação
      this.Motion.animate(
        section.element,
        {
          opacity: 0,
          y: 30,
          scale: 0.95,
        },
        { duration: 0 }
      );

      // Anima entrada
      await this.Motion.animate(
        section.element,
        {
          opacity: [0, 1],
          y: [30, 0],
          scale: [0.95, 1],
        },
        {
          duration: 0.4,
          ease: 'backOut',
        }
      ).finished;
    } else {
      // Mostra sem animação
      section.element.style.opacity = '1';
      section.element.style.transform = 'none';
    }

    section.visible = true;
    section.animating = false;
  }

  async toggleSection(sectionName) {
    if (!this.sections.has(sectionName)) {
      return;
    }

    const section = this.sections.get(sectionName);

    if (section.visible) {
      await this.hideSection(sectionName);
    } else {
      await this.showSection(sectionName);
    }
  }

  updateURL(sectionName) {
    if (sectionName !== 'calculadora') {
      window.history.pushState(null, null, `#${sectionName}`);
    } else {
      window.history.pushState(null, null, window.location.pathname);
    }
  }

  updateActiveStates(sectionName) {
    // Remove estado ativo de todos os triggers
    this.triggers.forEach((elements) => {
      elements.forEach((element) => {
        element.classList.remove('is-active');
      });
    });

    // Adiciona estado ativo aos triggers da seção atual
    const showTriggers = this.triggers.get(`show-${sectionName}`);
    if (showTriggers) {
      showTriggers.forEach((element) => {
        element.classList.add('is-active');
      });
    }
  }

  handleKeyboardShortcuts(e) {
    // Atalhos de teclado para navegação
    if (e.ctrlKey || e.metaKey) {
      switch (e.key) {
        case '1':
          e.preventDefault();
          this.showSection('calculadora');
          break;
        case '2':
          e.preventDefault();
          this.showSection('resultado');
          break;
        case '3':
          e.preventDefault();
          this.showSection('alocacao');
          break;
        case '4':
          e.preventDefault();
          this.showSection('detalhes');
          break;
        case '5':
          e.preventDefault();
          this.showSection('configuracoes');
          break;
      }
    }

    // ESC para voltar à calculadora
    if (e.key === 'Escape') {
      this.showSection('calculadora');
    }
  }

  handleHashChange() {
    const hash = window.location.hash.replace('#', '');
    if (hash && this.sections.has(hash) && hash !== this.currentSection) {
      this.showSection(hash);
    } else if (!hash && this.currentSection !== 'calculadora') {
      this.showSection('calculadora');
    }
  }

  // Métodos públicos para controle externo

  getCurrentSection() {
    return this.currentSection;
  }

  isSectionVisible(sectionName) {
    const section = this.sections.get(sectionName);
    return section ? section.visible : false;
  }

  getSectionElement(sectionName) {
    const section = this.sections.get(sectionName);
    return section ? section.element : null;
  }

  addCustomSection(name, selector) {
    const element = document.querySelector(selector);
    if (element) {
      this.sections.set(name, {
        element,
        selector,
        visible: false,
        animating: false,
      });
      return true;
    }
    return false;
  }

  addCustomTrigger(name, selector, targetSection) {
    const elements = document.querySelectorAll(selector);
    if (elements.length > 0) {
      this.triggers.set(name, Array.from(elements));

      // Adiciona event listeners
      elements.forEach((element) => {
        element.addEventListener('click', (e) => {
          e.preventDefault();
          this.showSection(targetSection);
        });
      });

      return true;
    }
    return false;
  }
}
