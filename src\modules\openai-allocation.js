/**
 * OpenAI Allocation System
 * Handles asset allocation suggestions using OpenAI API
 */
export class OpenAIAllocationSystem {
  constructor() {
    this.isInitialized = false;
    this.isGenerating = false;
    this.currentValue = 0;
    this.currentProfile = 'conservador';
    this.Button = null;
    this.Button1 = null;
    this.Button2 = null;
    this.Button3 = null;
    this.Button4 = null;
    this.Button5 = null;
    this.ButtonStop = null;
    this.ButtonSend = null;
    this.LoadingIndicator = null;
    this.SliderContainer = null;
    this.InputElement = null;
    this.ResultContainer = null;
    this.ResultText = null;
    this.PromptInput = null;
    this.ProcessPromptButton = null;
    this.patrimonySync = null;
  }

  init() {
    if (this.isInitialized) {
      return;
    }

    document.addEventListener('DOMContentLoaded', () => {
      this.initElements();
      this.setupEventListeners();
    });

    this.isInitialized = true;
  }

  initElements() {
    // Inicializa elementos DOM
    this.Button = document.querySelector('[data-button="1"]');
    this.Button1 = document.querySelector('[data-button="1"]');
    this.Button2 = document.querySelector('[data-button="2"]');
    this.Button3 = document.querySelector('[data-button="3"]');
    this.Button4 = document.querySelector('[data-button="4"]');
    this.Button5 = document.querySelector('[data-button="5"]');
    this.ButtonStop = document.querySelector('[data-button="stop"]');
    this.ButtonSend = document.querySelector('[data-button="send"]');
    this.LoadingIndicator = document.querySelector('[data-element="loading"]');
    this.SliderContainer = document.querySelector('.grupo_slider_system');
    this.InputElement = document.querySelector('.currency-input[is-main="true"]');
    this.ResultContainer = document.querySelector('[data-element="result"]');
    this.ResultText = document.querySelector('[data-element="result-text"]');
    this.PromptInput = document.querySelector('.prompt-input');
    this.ProcessPromptButton = document.querySelector('.process-prompt');

    // Estado inicial
    if (this.LoadingIndicator) {
      this.LoadingIndicator.style.display = 'none';
    }
    if (this.ButtonStop) {
      this.ButtonStop.style.display = 'none';
    }

    // Obtém referência ao sistema de patrimônio
    this.getPatrimonySync();

    // Expõe funções de debug em desenvolvimento
    this.exposeDebugFunctions();

    // Log de inicialização
    console.warn('🤖 OpenAI Allocation System inicializado');
    console.warn('💡 Use debugOpenAI.checkElements() para verificar integração');
  }

  setupEventListeners() {
    // Botões de perfil
    const profileButtons = [
      { button: this.Button1, profile: 'conservador' },
      { button: this.Button2, profile: 'moderado' },
      { button: this.Button3, profile: 'arrojado' },
      { button: this.Button4, profile: 'super-arrojado' },
      { button: this.Button5, profile: 'personalizado' },
    ];

    profileButtons.forEach(({ button, profile }) => {
      if (button) {
        button.addEventListener('click', () => {
          this.selectProfile(profile);
        });
      }
    });

    // Botão de envio
    if (this.ButtonSend) {
      this.ButtonSend.addEventListener('click', () => {
        this.generateAllocation();
      });
    }

    // Botão de parar
    if (this.ButtonStop) {
      this.ButtonStop.addEventListener('click', () => {
        this.stopGeneration();
      });
    }

    // Input de valor principal
    if (this.InputElement) {
      this.InputElement.addEventListener('input', () => {
        this.updateValue();
      });
    } else {
      // Fallback: tenta encontrar qualquer input principal
      const fallbackInput = document.querySelector(
        '#currency, [data-currency="true"][is-main="true"]'
      );
      if (fallbackInput) {
        this.InputElement = fallbackInput;
        this.InputElement.addEventListener('input', () => {
          this.updateValue();
        });
      }
    }

    // Prompt personalizado - botão process-prompt
    if (this.ProcessPromptButton) {
      this.ProcessPromptButton.addEventListener('click', () => {
        this.generateCustomAllocation();
      });
    }

    // Prompt personalizado - Enter key no input
    if (this.PromptInput) {
      this.PromptInput.addEventListener('keydown', (e) => {
        if (e.key === 'Enter') {
          e.preventDefault();
          this.generateCustomAllocation();
        }
      });
    }
  }

  selectProfile(profile) {
    this.currentProfile = profile;

    // Atualiza estado visual dos botões
    const buttons = [this.Button1, this.Button2, this.Button3, this.Button4, this.Button5];
    buttons.forEach((button) => {
      if (button) {
        button.classList.remove('is-selected');
      }
    });

    const selectedButton = document.querySelector(
      `[data-button="${this.getProfileNumber(profile)}"]`
    );
    if (selectedButton) {
      selectedButton.classList.add('is-selected');
    }
  }

  getProfileNumber(profile) {
    const profileMap = {
      conservador: '1',
      moderado: '2',
      arrojado: '3',
      'super-arrojado': '4',
      personalizado: '5',
    };
    return profileMap[profile] || '1';
  }

  updateValue() {
    if (this.InputElement) {
      const rawValue = this.InputElement.value.replace(/[^\d]/g, '');
      this.currentValue = parseFloat(rawValue) || 0;
    }
  }

  async generateAllocation() {
    if (this.isGenerating) {
      return;
    }

    if (this.currentValue === 0) {
      console.warn('Por favor, insira um valor válido para o patrimônio.');
      return;
    }

    this.isGenerating = true;
    this.showLoading();

    try {
      const allocation = await this.callOpenAI();
      this.displayResult(allocation);
    } catch (error) {
      this.displayError(error.message);
    } finally {
      this.hideLoading();
      this.isGenerating = false;
    }
  }

  async generateCustomAllocation() {
    if (this.isGenerating) {
      return;
    }

    const customPrompt = this.PromptInput?.value?.trim();
    if (!customPrompt) {
      console.warn('Por favor, descreva como deseja alocar seu patrimônio.');
      return;
    }

    // Extrai valor do patrimônio do texto se não estiver definido no input principal
    let patrimonyValue = this.currentValue;
    if (patrimonyValue === 0) {
      patrimonyValue = this.extractPatrimonyValueFromText(customPrompt);
      if (patrimonyValue > 0) {
        // Usa o valor extraído apenas para os cálculos, SEM atualizar o input principal
        this.currentValue = patrimonyValue;
        console.warn(`💰 Valor extraído do texto: ${this.formatCurrency(patrimonyValue)}`);
      } else {
        this.showToast(
          '⚠️',
          'Por favor, defina seu patrimônio no campo principal ou mencione no texto (ex: "tenho R$ 100.000")',
          'warning'
        );
        return;
      }
    }

    this.isGenerating = true;
    this.showLoading();

    try {
      const allocation = await this.callOpenAIWithCustomPrompt(customPrompt);
      this.displayResult(allocation);

      // Processa a alocação e atualiza os inputs
      await this.processAllocationResponse(allocation);

      // Mostra feedback visual
      this.showAllocationAppliedFeedback();

      // Limpa o input após processar
      if (this.PromptInput) {
        this.PromptInput.value = '';
      }
    } catch (error) {
      this.displayError(error.message);
    } finally {
      this.hideLoading();
      this.isGenerating = false;
    }
  }

  async callOpenAI() {
    const prompt = this.buildPrompt();

    const response = await window.fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${this.getAPIKey()}`,
      },
      body: JSON.stringify({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content:
              'Você é um consultor financeiro especializado em alocação de ativos no mercado brasileiro. Seja muito flexível com linguagem natural, informal e coloquial. Interprete a intenção do usuário mesmo com erros de gramática ou valores pequenos. Sempre forneça sugestões construtivas independente do valor mencionado.',
          },
          {
            role: 'user',
            content: prompt,
          },
        ],
        max_tokens: 1500,
        temperature: 0.8,
        top_p: 0.9,
        frequency_penalty: 0.2,
      }),
    });

    if (!response.ok) {
      throw new Error(`Erro na API: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data.choices[0].message.content;
  }

  async callOpenAIWithCustomPrompt(customPrompt) {
    const prompt = this.buildCustomPrompt(customPrompt);

    const response = await window.fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${this.getAPIKey()}`,
      },
      body: JSON.stringify({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content:
              'Você é um consultor financeiro especializado em alocação de ativos no mercado brasileiro. Seja muito flexível com linguagem natural, informal e coloquial. Interprete a intenção do usuário mesmo com erros de gramática, gírias ou valores pequenos. Sempre forneça sugestões específicas, percentuais claros e justificativas construtivas, independente do valor disponível.',
          },
          {
            role: 'user',
            content: prompt,
          },
        ],
        max_tokens: 1500,
        temperature: 0.8,
        top_p: 0.9,
        frequency_penalty: 0.2,
        presence_penalty: 0.1,
      }),
    });

    if (!response.ok) {
      throw new Error(`Erro na API: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data.choices[0].message.content;
  }

  async processAllocationResponse(allocationText) {
    try {
      // Extrai percentuais da resposta da IA
      const allocations = this.extractAllocationsFromText(allocationText);

      if (allocations.length > 0) {
        await this.updatePatrimonyItems(allocations);
        return true;
      }
      this.showNoAllocationFoundWarning();
      return false;
    } catch (error) {
      console.warn('Erro ao processar alocação automática:', error);
      this.showAllocationError(error.message);
      return false;
    }
  }

  extractAllocationsFromText(text) {
    const allocations = [];

    // Primeiro, tenta extrair percentuais diretamente do input do usuário (mais prioritário)
    const userPercentages = this.extractUserPercentages(text);
    if (userPercentages.length > 0) {
      console.warn(`📊 Percentuais específicos encontrados: ${userPercentages.length} itens`);
      return userPercentages;
    }

    // Se não encontrou percentuais diretos, processa resposta da IA
    const patterns = [
      // Padrão principal: "Categoria: X%" ou "Categoria X%"
      /(renda\s+fixa|fundos?\s+de\s+investimento|renda\s+variável|outros?\s+investimentos?|cdb|lci|lca|tesouro|títulos?\s+públicos?|cri|cra|debêntures?|ações|fiis?|etfs?|estruturada?|criptomoedas?|commodities)[\s\-:]*(\d+(?:[,.]\d+)?)\s*%/gi,
      // Padrão secundário: "X% para Categoria"
      /(\d+(?:[,.]\d+)?)\s*%\s*(?:para|em|de|do|da)?\s*(renda\s+fixa|fundos?\s+de\s+investimento|renda\s+variável|outros?\s+investimentos?|cdb|lci|lca|tesouro|títulos?\s+públicos?|cri|cra|debêntures?|ações|fiis?|etfs?|estruturada?|criptomoedas?|commodities)/gi,
      // Padrão bullet point: "• Categoria: X%"
      /[•\-*]\s*(renda\s+fixa|fundos?\s+de\s+investimento|renda\s+variável|outros?\s+investimentos?|cdb|lci|lca|tesouro|títulos?\s+públicos?|cri|cra|debêntures?|ações|fiis?|etfs?|estruturada?|criptomoedas?|commodities)[\s\-:]*(\d+(?:[,.]\d+)?)\s*%/gi,
    ];

    patterns.forEach((pattern) => {
      let match;
      while ((match = pattern.exec(text)) !== null) {
        let percentage, category;

        if (match[1] && isNaN(match[1])) {
          // Categoria vem primeiro
          category = match[1].toLowerCase().trim();
          percentage = parseFloat(match[2].replace(',', '.'));
        } else {
          // Percentual vem primeiro
          percentage = parseFloat(match[1].replace(',', '.'));
          category = match[2].toLowerCase().trim();
        }

        if (percentage > 0 && percentage <= 100) {
          const matchedItem = this.findBestMatchingItem(category);

          if (matchedItem !== -1) {
            // Evita duplicatas
            const exists = allocations.find((a) => a.category === matchedItem);
            if (!exists) {
              allocations.push({
                percentage: percentage,
                category: matchedItem,
                originalCategory: category,
              });
            }
          } else {
            console.warn(`⚠️ Categoria não mapeada: "${category}"`);
          }
        }
      }
    });

    // Se não encontrou alocações estruturadas, avisa que não conseguiu interpretar
    if (allocations.length === 0) {
      console.warn('⚠️ Nenhuma alocação específica encontrada no texto da IA');
    }

    return allocations;
  }

  extractUserPercentages(text) {
    const allocations = [];
    const normalizedText = text.toLowerCase().trim();

    // Padrões específicos para entrada do usuário com percentuais
    const userPatterns = [
      // "tenho 30% em cdb", "quero 50% cdb"
      /(?:tenho|quero|coloco|ponho|aplico|invisto|aloco)\s*(\d+(?:[,.]\d+)?)\s*%\s*(?:em|no|na|de|do|da)?\s*(cdb|lci|lca|tesouro|títulos?\s*públicos?|cri|cra|debêntures?|ações|estruturada?|fundos?)/gi,
      // "30% em cdb", "50% cdb"
      /(\d+(?:[,.]\d+)?)\s*%\s*(?:em|no|na|de|do|da|para)?\s*(cdb|lci|lca|tesouro|títulos?\s*públicos?|cri|cra|debêntures?|ações|estruturada?|fundos?)/gi,
      // "cdb 30%", "ações 50%"
      /(cdb|lci|lca|tesouro|títulos?\s*públicos?|cri|cra|debêntures?|ações|estruturada?|fundos?)\s*(\d+(?:[,.]\d+)?)\s*%/gi,
    ];

    userPatterns.forEach((pattern) => {
      let match;
      while ((match = pattern.exec(normalizedText)) !== null) {
        let percentage, category;

        if (isNaN(match[1])) {
          // Categoria vem primeiro: "cdb 30%"
          category = match[1].toLowerCase().trim();
          percentage = parseFloat(match[2].replace(',', '.'));
        } else {
          // Percentual vem primeiro: "30% em cdb"
          percentage = parseFloat(match[1].replace(',', '.'));
          category = match[2].toLowerCase().trim();
        }

        if (percentage > 0 && percentage <= 100) {
          const matchedItem = this.findBestMatchingItem(category);

          if (matchedItem !== -1) {
            // Evita duplicatas
            const exists = allocations.find((a) => a.category === matchedItem);
            if (!exists) {
              allocations.push({
                percentage: percentage,
                category: matchedItem,
                originalCategory: category,
                isDirectUserInput: true, // Marca como entrada direta do usuário
              });
              console.warn(
                `📊 Percentual direto: ${percentage}% em ${category} → item[${matchedItem}]`
              );
            }
          } else {
            console.warn(`⚠️ Categoria não mapeada (input usuário): "${category}"`);
          }
        }
      }
    });

    return allocations;
  }

  findBestMatchingItem(category) {
    const normalizedCategory = category.toLowerCase().trim();
    const patrimonyItems = this.getPatrimonyItems();

    let bestMatch = -1;
    let bestScore = 0;

    patrimonyItems.forEach((item, index) => {
      if (!item) return;

      const score = this.calculateMatchScore(normalizedCategory, item);
      if (score > bestScore) {
        bestScore = score;
        bestMatch = index;
      }
    });

    // Se não encontrou match, usa mapeamento legacy
    if (bestMatch === -1) {
      bestMatch = this.mapCategoryToIndexLegacy(normalizedCategory);
    }

    return bestMatch;
  }

  calculateMatchScore(searchTerm, item) {
    let score = 0;

    const product = item.ativoProduct.toLowerCase();
    const category = item.ativoCategory.toLowerCase();

    // Score para correspondências exatas
    if (product === searchTerm) score += 100;
    if (category === searchTerm) score += 80;

    // Score para correspondências parciais no produto
    if (product.includes(searchTerm)) score += 60;
    if (searchTerm.includes(product)) score += 50;

    // Score para correspondências parciais na categoria
    if (category.includes(searchTerm)) score += 40;
    if (searchTerm.includes(category)) score += 30;

    // Scores específicos para termos conhecidos
    const specificMatches = {
      cdb: ['cdb'],
      lci: ['cdb', 'lci'],
      lca: ['cdb', 'lca'],
      tesouro: ['títulos públicos', 'tesouro'],
      'títulos públicos': ['títulos públicos'],
      cri: ['cri'],
      cra: ['cri', 'cra'],
      debenture: ['cri'],
      debênture: ['cri'],
      ações: ['ações'],
      estruturada: ['estruturada'],
      'renda fixa': ['renda fixa'],
      'renda variável': ['renda variável'],
      'fundo de investimento': ['fundo de investimento'],
      outros: ['outros'],
    };

    if (specificMatches[searchTerm]) {
      specificMatches[searchTerm].forEach((term) => {
        if (product.includes(term) || category.includes(term)) {
          score += 70;
        }
      });
    }

    return score;
  }

  mapCategoryToIndexLegacy(category) {
    // Fallback para o sistema antigo
    const categoryMap = {
      'renda fixa': 0,
      cdb: 0,
      lci: 0,
      lca: 0,
      tesouro: 1,
      'títulos públicos': 1,
      cri: 2,
      cra: 2,
      debenture: 2,
      fundo: 3,
      ações: 6,
      estruturada: 7,
      outros: 9,
    };

    return categoryMap[category] || 0;
  }

  async updatePatrimonyItems(allocations) {
    if (!this.patrimonySync) {
      this.getPatrimonySync();
    }

    // Tenta reconectar se necessário
    if (!this.patrimonySync) {
      this.attemptConnection();
    }

    // Continua mesmo sem patrimonySync, pois podemos atualizar diretamente no DOM
    if (!this.patrimonySync) {
      console.warn('Sistema de patrimônio não conectado, aplicando diretamente no DOM');
    }

    const mainValue = this.currentValue;
    if (mainValue <= 0) {
      console.warn('Valor principal deve ser maior que zero');
      return;
    }

    // Obtém os itens do sistema de patrimônio
    const patrimonyItems = this.getPatrimonyItems();

    // Primeiro, zera todos os valores para começar limpo
    await this.clearAllAllocations(patrimonyItems);

    // Aplica as novas alocações com animação sequencial
    for (let i = 0; i < allocations.length; i++) {
      const { percentage, category } = allocations[i];
      const value = (mainValue * percentage) / 100;

      if (patrimonyItems[category]) {
        await this.setItemValueAnimated(patrimonyItems[category], value, percentage, i * 100);
      }
    }

    // Dispara evento para atualizar totais
    this.triggerPatrimonyUpdate();
  }

  getPatrimonySync() {
    // Sistema de reconexão automática
    this.attemptConnection();

    // Inicia verificação contínua
    this.startConnectionMonitoring();
  }

  attemptConnection() {
    if (window.AppCalcReino) {
      const module = window.AppCalcReino.getModule('patrimonySync');
      if (module && !this.patrimonySync) {
        this.patrimonySync = module;
        console.warn('✅ Sistema de patrimônio conectado');
        return true;
      }
    }
    return false;
  }

  startConnectionMonitoring() {
    let attempts = 0;
    const maxAttempts = 10;
    const interval = 500;

    const monitor = setInterval(() => {
      attempts += 1;

      if (this.patrimonySync) {
        clearInterval(monitor);
        console.warn('🔗 Monitoramento de conexão finalizado - Sistema conectado');
        return;
      }

      if (this.attemptConnection()) {
        clearInterval(monitor);
        return;
      }

      if (attempts >= maxAttempts) {
        clearInterval(monitor);
        console.warn('⚠️ Sistema de patrimônio não encontrado após múltiplas tentativas');
        console.warn('💡 Use debugOpenAI.checkElements() para diagnóstico');
      }
    }, interval);
  }

  getPatrimonyItems() {
    const items = [];
    const containers = document.querySelectorAll('.patrimonio_interactive_item');

    containers.forEach((container, index) => {
      // Busca especificamente por input com input-settings="receive"
      const input = container.querySelector('input[input-settings="receive"]');
      const slider = container.querySelector('range-slider');
      const percentageDisplay = container.querySelector('.porcentagem-calculadora');

      // Extrai atributos de identificação
      const ativoProduct = container.getAttribute('ativo-product');
      const ativoCategory = container.getAttribute('ativo-category');

      // Fallback para texto interno se não tiver atributos
      const categoryText = container.querySelector('.categoria-ativo')?.textContent?.trim();
      const productText = container
        .querySelector('.ativo_alocated_top-wrapper > div:nth-child(2)')
        ?.textContent?.trim();

      if (input && slider) {
        items[index] = {
          container,
          input,
          slider,
          percentageDisplay,
          index,
          ativoProduct: ativoProduct || productText || '',
          ativoCategory: ativoCategory || categoryText || '',
        };
      }
    });

    return items;
  }

  async clearAllAllocations(patrimonyItems) {
    const promises = patrimonyItems.map((item, index) => {
      if (item) {
        return this.setItemValueAnimated(item, 0, 0, index * 50);
      }
    });

    await Promise.all(promises.filter(Boolean));
  }

  async setItemValueAnimated(item, value, percentage, delay = 0) {
    if (!item || !item.input || !item.slider) return;

    return new Promise((resolve) => {
      setTimeout(() => {
        try {
          // Adiciona classe de highlight
          item.container.classList.add('ai-updating');

          // Atualiza o input com valor formatado
          const formattedValue = new Intl.NumberFormat('pt-BR', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          }).format(value);

          item.input.value = formattedValue;

          // Atualiza o slider (valor entre 0 e 1)
          const sliderValue = Math.min(percentage / 100, 1);
          item.slider.value = sliderValue;

          // Atualiza o display de porcentagem
          if (item.percentageDisplay) {
            item.percentageDisplay.textContent = `${percentage.toFixed(1)}%`;
          }

          // Dispara eventos de mudança para sincronizar com outros sistemas
          const inputEvent = new Event('input', { bubbles: true });
          const changeEvent = new Event('change', { bubbles: true });

          item.input.dispatchEvent(inputEvent);
          item.input.dispatchEvent(changeEvent);
          item.slider.dispatchEvent(inputEvent);
          item.slider.dispatchEvent(changeEvent);

          // Remove classe de highlight após animação
          setTimeout(() => {
            item.container.classList.remove('ai-updating');
            resolve();
          }, 300);
        } catch (error) {
          console.warn('Erro ao atualizar item patrimonial:', error);
          resolve();
        }
      }, delay);
    });
  }

  setItemValue(item, value, percentage) {
    return this.setItemValueAnimated(item, value, percentage, 0);
  }

  triggerPatrimonyUpdate() {
    // Dispara evento customizado para notificar outros sistemas
    const event = new CustomEvent('aiAllocationUpdate', {
      detail: {
        source: 'openai',
        timestamp: Date.now(),
      },
    });

    document.dispatchEvent(event);

    // Tenta sincronizar com patrimonySync se disponível
    if (this.patrimonySync && typeof this.patrimonySync.updateAllAllocations === 'function') {
      try {
        this.patrimonySync.updateAllAllocations();
        console.warn('✅ Sincronização com patrimonySync realizada');
      } catch (error) {
        console.warn('Erro ao sincronizar com patrimonySync:', error);
      }
    } else if (!this.patrimonySync) {
      // Tenta reconectar antes de desistir
      if (this.attemptConnection() && this.patrimonySync.updateAllAllocations) {
        this.patrimonySync.updateAllAllocations();
        console.warn('✅ Reconectado e sincronizado com patrimonySync');
      }
    }
  }

  showAllocationAppliedFeedback() {
    // Cria um toast de feedback
    const toast = document.createElement('div');
    toast.className = 'ai-allocation-toast';
    toast.innerHTML = `
      <div class="toast-content">
        <div class="toast-icon">✅</div>
        <div class="toast-message">Alocação aplicada com sucesso!</div>
      </div>
    `;

    // Adiciona estilos inline para o toast
    toast.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: linear-gradient(135deg, #4CAF50, #45a049);
      color: white;
      padding: 16px 20px;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      z-index: 10000;
      animation: slideInRight 0.3s ease-out forwards;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      max-width: 300px;
    `;

    // Adiciona animação CSS
    if (!document.getElementById('ai-toast-styles')) {
      const style = document.createElement('style');
      style.id = 'ai-toast-styles';
      style.textContent = `
        @keyframes slideInRight {
          from { transform: translateX(100%); opacity: 0; }
          to { transform: translateX(0); opacity: 1; }
        }
        @keyframes slideOutRight {
          from { transform: translateX(0); opacity: 1; }
          to { transform: translateX(100%); opacity: 0; }
        }
        .toast-content {
          display: flex;
          align-items: center;
          gap: 10px;
        }
        .toast-icon {
          font-size: 16px;
        }
        .ai-updating {
          background: linear-gradient(90deg, rgba(76, 175, 80, 0.1), rgba(76, 175, 80, 0.2), rgba(76, 175, 80, 0.1));
          background-size: 200% 100%;
          animation: aiUpdatePulse 1s ease-in-out;
          border: 2px solid rgba(76, 175, 80, 0.3);
          border-radius: 8px;
        }
        @keyframes aiUpdatePulse {
          0% { background-position: 200% 0; }
          100% { background-position: -200% 0; }
        }
      `;
      document.head.appendChild(style);
    }

    document.body.appendChild(toast);

    // Remove o toast após 3 segundos
    setTimeout(() => {
      toast.style.animation = 'slideOutRight 0.3s ease-in forwards';
      setTimeout(() => {
        if (toast.parentNode) {
          toast.parentNode.removeChild(toast);
        }
      }, 300);
    }, 3000);
  }

  showNoAllocationFoundWarning() {
    this.showToast('⚠️', 'Não foi possível extrair alocações do texto da IA', 'warning');
  }

  showAllocationError(message) {
    this.showToast('❌', `Erro ao aplicar alocação: ${message}`, 'error');
  }

  showToast(icon, message, type = 'success') {
    const colors = {
      success: { bg: 'linear-gradient(135deg, #4CAF50, #45a049)', border: '#4CAF50' },
      warning: { bg: 'linear-gradient(135deg, #FF9800, #F57C00)', border: '#FF9800' },
      error: { bg: 'linear-gradient(135deg, #f44336, #d32f2f)', border: '#f44336' },
    };

    const toast = document.createElement('div');
    toast.innerHTML = `
      <div class="toast-content">
        <div class="toast-icon">${icon}</div>
        <div class="toast-message">${message}</div>
      </div>
    `;

    toast.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: ${colors[type].bg};
      color: white;
      padding: 16px 20px;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      z-index: 10000;
      animation: slideInRight 0.3s ease-out forwards;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      max-width: 350px;
    `;

    document.body.appendChild(toast);

    setTimeout(
      () => {
        toast.style.animation = 'slideOutRight 0.3s ease-in forwards';
        setTimeout(() => {
          if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
          }
        }, 300);
      },
      type === 'error' ? 5000 : 3000
    );
  }

  buildPrompt() {
    const profileDescriptions = {
      conservador: 'perfil conservador (baixo risco, foco em renda fixa)',
      moderado: 'perfil moderado (risco equilibrado entre renda fixa e variável)',
      arrojado: 'perfil arrojado (maior exposição à renda variável)',
      'super-arrojado': 'perfil super arrojado (alta exposição a ativos de risco)',
      personalizado: 'perfil personalizado',
    };

    return `
      Preciso de uma sugestão de alocação de ativos para um investidor com ${profileDescriptions[this.currentProfile]}
      e patrimônio total de R$ ${this.formatCurrency(this.currentValue)}.

      Por favor, forneça:
      1. Percentuais específicos para cada classe de ativo
      2. Sugestão de produtos/fundos para cada categoria
      3. Justificativa da alocação baseada no perfil

      Considere o cenário econômico atual do Brasil e as melhores práticas de diversificação.

      Formato da resposta: estruturado e profissional, adequado para apresentação ao cliente.
    `;
  }

  hasUserPercentages(text) {
    const normalizedText = text.toLowerCase();
    // Verifica se o usuário mencionou percentuais específicos
    const percentagePattern = /\d+(?:[,.]\d+)?\s*%/;
    return percentagePattern.test(normalizedText);
  }

  buildCustomPrompt(customPrompt) {
    // Verifica se o texto contém valor do patrimônio
    const extractedValue = this.extractPatrimonyValueFromText(customPrompt);
    const patrimonyValue = extractedValue > 0 ? extractedValue : this.currentValue;

    return `
      Analise a seguinte solicitação de alocação de patrimônio do investidor:

      "${customPrompt}"

      ${patrimonyValue > 0 ? `O patrimônio identificado/disponível é de R$ ${this.formatCurrency(patrimonyValue)}.` : ''}

      INSTRUÇÕES IMPORTANTES:
      - Seja muito flexível com linguagem natural e coloquial
      - Interprete a intenção do usuário mesmo com gramática informal
      - Se o usuário mencionar valores pequenos (ex: "20 reais"), trate como um caso real e faça sugestões adequadas
      - Adapte as recomendações ao valor disponível, mesmo que seja pequeno
      - Use produtos acessíveis para valores menores (ex: CDB, poupança, fundos com aplicação mínima baixa)

      ${
        this.hasUserPercentages(customPrompt)
          ? 'O usuário especificou percentuais diretos. RESPEITE EXATAMENTE os percentuais mencionados e complete apenas o que faltou para somar 100%.'
          : 'Com base nesta solicitação, por favor forneça uma resposta estruturada seguindo EXATAMENTE este formato:'
      }

      **ALOCAÇÃO SUGERIDA:**
      • Renda Fixa: X%
      • Fundos de Investimento: Y%
      • Renda Variável: Z%
      • Outros Investimentos: W%

      **DETALHAMENTO POR CATEGORIA:**
      1. **Renda Fixa (X%):**
         - CDB: A%
         - CRI: B%
         - Títulos Públicos: C%

      2. **Fundos de Investimento (Y%):**
         - Ações: D%
         - Renda Fixa: E%

      3. **Renda Variável (Z%):**
         - Ações: F%
         - Estruturada: G%

      4. **Outros Investimentos (W%):**
         - [Especificar conforme solicitação]: H%

      **JUSTIFICATIVA:**
      [Explicação da estratégia baseada na solicitação do cliente]

      **OBSERVAÇÕES:**
      [Considerações de risco e adequação ao perfil]

      IMPORTANTE:
      - Use SEMPRE percentuais numéricos precisos. Exemplo: "30%" não "cerca de 30%".
      - Use os termos EXATOS: "CDB", "CRI", "Títulos Públicos", "Ações", "Estruturada", "Renda Fixa".
      - Para fundos, especifique se são "Ações" ou "Renda Fixa".
      - Se o usuário mencionar percentuais específicos (ex: "30% em CDB"), respeite EXATAMENTE.
      - Seja muito flexível com linguagem natural e interprete a intenção do usuário.
      - Para valores pequenos, foque em produtos acessíveis.
      - Sempre forneça uma sugestão construtiva, independente do valor.
    `;
  }

  extractPatrimonyValueFromText(text) {
    // Normaliza o texto para facilitar extração
    const normalizedText = text.toLowerCase().replace(/\s+/g, ' ').trim();

    // Padrões mais abrangentes para identificar valores monetários
    const patterns = [
      // "tenho 20 reais", "possuo 100 mil", "tenho R$ 50.000"
      /(?:tenho|possuo|disponho|tenho disponível|meu patrimônio é|patrimônio de|com|valor de|total de)\s*(?:de\s*|um\s*)?(?:r\$|reais?\s*)?\s*([\d.,]+)(?:\s*(?:mil|k|thousand|reais?))?/gi,
      // "R$ 100.000", "R$ 50 mil", "20 reais"
      /(?:r\$|reais?)\s*([\d.,]+)(?:\s*(?:mil|k|thousand))?/gi,
      // "100 mil reais", "50 mil", "20 reais"
      /([\d.,]+)\s*(?:mil|k|thousand)?\s*(?:reais?|r\$)?/gi,
      // Números simples no contexto (mais de 2 dígitos)
      /(\d{3,}(?:[.,]\d{3})*(?:[.,]\d{2})?)/g,
      // Valores escritos por extenso com números
      /(\d+)\s*(?:mil|milhões?|bilhões?)/gi,
    ];

    let values = [];

    patterns.forEach((pattern) => {
      let match;
      while ((match = pattern.exec(normalizedText)) !== null) {
        let value = this.parseValueFromMatch(match[1], match[0]);
        if (value > 0) {
          values.push(value);
        }
      }
    });

    // Se não encontrou valores, tenta padrões mais relaxados
    if (values.length === 0) {
      const relaxedPattern = /(\d+(?:[.,]\d+)*)/g;
      let match;
      while ((match = relaxedPattern.exec(normalizedText)) !== null) {
        let value = this.parseValueFromMatch(match[1], match[0]);
        // Só aceita se for um valor razoável para patrimônio
        if (value >= 10 && value <= 1000000000) {
          values.push(value);
        }
      }
    }

    // Retorna o maior valor encontrado, assumindo que é o patrimônio total
    return values.length > 0 ? Math.max(...values) : 0;
  }

  parseValueFromMatch(valueStr, fullMatch) {
    // Remove pontos e vírgulas para normalizar
    let cleanValue = valueStr.replace(/[.,]/g, '');
    let value = parseFloat(cleanValue);

    if (isNaN(value)) return 0;

    const fullMatchLower = fullMatch.toLowerCase();

    // Identifica multiplicadores
    if (fullMatchLower.includes('milhões') || fullMatchLower.includes('milhao')) {
      value = value * 1000000;
    } else if (fullMatchLower.includes('bilhões') || fullMatchLower.includes('bilhao')) {
      value = value * 1000000000;
    } else if (
      fullMatchLower.includes('mil') ||
      fullMatchLower.includes('k') ||
      fullMatchLower.includes('thousand')
    ) {
      value = value * 1000;
    } else {
      // Heurística: se o valor tem 1-3 dígitos, provavelmente está em milhares
      if (value >= 10 && value <= 999 && !fullMatchLower.includes('reais')) {
        value = value * 1000;
      }
      // Se tem vírgula/ponto no original, pode ser decimal
      if (valueStr.includes(',') || valueStr.includes('.')) {
        const parts = valueStr.split(/[.,]/);
        if (parts.length === 2 && parts[1].length === 2) {
          // Formato decimal: 100,50 = 100.50
          value = parseFloat(parts[0]) + parseFloat(parts[1]) / 100;
        }
      }
    }

    return value;
  }

  // REMOVIDO: Não devemos atualizar o input principal
  // O usuário define seu patrimônio no input principal, não devemos alterar

  formatCurrency(value) {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(value);
  }

  getAPIKey() {
    // Em produção, esta chave deve vir de variáveis de ambiente seguras
    return (
      window.process?.env?.OPENAI_API_KEY ||
      '********************************************************************************************************************************************************************'
    );
  }

  showLoading() {
    if (this.LoadingIndicator) {
      this.LoadingIndicator.style.display = 'block';
    }
    if (this.ButtonSend) {
      this.ButtonSend.style.display = 'none';
    }
    if (this.ButtonStop) {
      this.ButtonStop.style.display = 'block';
    }
    if (this.ResultContainer) {
      this.ResultContainer.style.display = 'none';
    }

    // Desabilita prompt input e botão durante loading
    if (this.PromptInput) {
      this.PromptInput.disabled = true;
      this.PromptInput.style.opacity = '0.6';
      this.PromptInput.style.cursor = 'not-allowed';
    }
    if (this.ProcessPromptButton) {
      this.ProcessPromptButton.disabled = true;
      this.ProcessPromptButton.style.opacity = '0.6';
      this.ProcessPromptButton.style.cursor = 'not-allowed';
      this.ProcessPromptButton.innerHTML = '<div>Processando...</div>';
    }
  }

  hideLoading() {
    if (this.LoadingIndicator) {
      this.LoadingIndicator.style.display = 'none';
    }
    if (this.ButtonSend) {
      this.ButtonSend.style.display = 'block';
    }
    if (this.ButtonStop) {
      this.ButtonStop.style.display = 'none';
    }

    // Reabilita prompt input e botão após loading
    if (this.PromptInput) {
      this.PromptInput.disabled = false;
      this.PromptInput.style.opacity = '1';
      this.PromptInput.style.cursor = 'text';
    }
    if (this.ProcessPromptButton) {
      this.ProcessPromptButton.disabled = false;
      this.ProcessPromptButton.style.opacity = '1';
      this.ProcessPromptButton.style.cursor = 'pointer';
      this.ProcessPromptButton.innerHTML = '<div>Alocar</div>';
    }
  }

  displayResult(allocation) {
    if (this.ResultText) {
      this.ResultText.innerHTML = this.formatAllocationText(allocation);
    }
    if (this.ResultContainer) {
      this.ResultContainer.style.display = 'block';
    }
  }

  displayError(errorMessage) {
    if (this.ResultText) {
      this.ResultText.innerHTML = `
        <div style="color: #e74c3c; padding: 20px; border-radius: 8px; background: #fdf2f2;">
          <h4>Erro na geração da alocação</h4>
          <p>${errorMessage}</p>
          <p>Por favor, tente novamente ou entre em contato com o suporte.</p>
        </div>
      `;
    }
    if (this.ResultContainer) {
      this.ResultContainer.style.display = 'block';
    }
  }

  formatAllocationText(text) {
    // Formata o texto da alocação com HTML adequado
    return text
      .replace(/\n\n/g, '</p><p>')
      .replace(/\n/g, '<br>')
      .replace(/^\s*/, '<p>')
      .replace(/\s*$/, '</p>')
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>');
  }

  stopGeneration() {
    this.isGenerating = false;
    this.hideLoading();

    // Limpa o input se houver algum processo de prompt personalizado
    if (this.PromptInput) {
      // Não limpa automaticamente, deixa o usuário decidir
      this.PromptInput.focus();
    }
  }

  // Método público para atualizar valor externamente
  updateCurrentValue(value) {
    this.currentValue = value;
  }

  // Método público para obter perfil atual
  getCurrentProfile() {
    return this.currentProfile;
  }

  // Método público para obter valor atual
  getCurrentValue() {
    return this.currentValue;
  }

  // Função de debug para visualizar mapeamento
  debugPatrimonyMapping() {
    const items = this.getPatrimonyItems();
    console.warn('=== MAPEAMENTO DE ITENS PATRIMONIAIS ===');

    items.forEach((item, index) => {
      if (item) {
        console.warn(`[${index}] ${item.ativoCategory} - ${item.ativoProduct}`);
        console.warn(`    Container:`, item.container);
        console.warn(`    Atributos:`, {
          'ativo-product': item.ativoProduct,
          'ativo-category': item.ativoCategory,
        });
        console.warn('---');
      } else {
        console.warn(`[${index}] ITEM VAZIO`);
      }
    });

    console.warn('=== FIM DO MAPEAMENTO ===');
  }

  // Função para testar correspondência de categorias
  testCategoryMapping(searchTerm) {
    const items = this.getPatrimonyItems();
    console.warn(`=== TESTE DE CORRESPONDÊNCIA: "${searchTerm}" ===`);

    items.forEach((item, index) => {
      if (item) {
        const score = this.calculateMatchScore(searchTerm.toLowerCase(), item);
        console.warn(`[${index}] Score: ${score} - ${item.ativoCategory} - ${item.ativoProduct}`);
      }
    });

    const bestMatch = this.findBestMatchingItem(searchTerm);
    console.warn(`MELHOR CORRESPONDÊNCIA: Índice ${bestMatch}`);
    if (bestMatch !== -1 && items[bestMatch]) {
      console.warn(
        `ITEM SELECIONADO: ${items[bestMatch].ativoCategory} - ${items[bestMatch].ativoProduct}`
      );
    }
    console.warn('=== FIM DO TESTE ===');
  }

  // Função para testar extração de valores
  testValueExtraction(text) {
    console.warn(`=== TESTE DE EXTRAÇÃO DE VALOR: "${text}" ===`);
    const extractedValue = this.extractPatrimonyValueFromText(text);
    console.warn(`VALOR EXTRAÍDO: R$ ${extractedValue}`);
    console.warn(`VALOR FORMATADO: ${this.formatCurrency(extractedValue)}`);
    console.warn('=== FIM DO TESTE ===');
    return extractedValue;
  }

  // Função para testar extração de percentuais
  testPercentageExtraction(text) {
    console.warn(`=== TESTE DE EXTRAÇÃO DE PERCENTUAIS: "${text}" ===`);

    // Testa percentuais diretos do usuário
    const userPercentages = this.extractUserPercentages(text);
    console.warn(`PERCENTUAIS DIRETOS ENCONTRADOS: ${userPercentages.length}`);
    userPercentages.forEach((item, index) => {
      console.warn(
        `  [${index}] ${item.percentage}% → ${item.originalCategory} (item[${item.category}])`
      );
    });

    // Testa extração geral (incluindo resposta da IA)
    const allAllocations = this.extractAllocationsFromText(text);
    console.warn(`TOTAL DE ALOCAÇÕES: ${allAllocations.length}`);
    allAllocations.forEach((item, index) => {
      const type = item.isDirectUserInput ? 'DIRETO' : 'IA';
      console.warn(
        `  [${index}] ${item.percentage}% → ${item.originalCategory} (${type}) [item${item.category}]`
      );
    });

    // Verifica se tem percentuais
    const hasPercentages = this.hasUserPercentages(text);
    console.warn(`CONTÉM PERCENTUAIS: ${hasPercentages ? 'SIM' : 'NÃO'}`);

    console.warn('=== FIM DO TESTE ===');
    return allAllocations;
  }

  // Função para verificar se elementos estão sendo encontrados
  debugElementsCheck() {
    console.warn('=== VERIFICAÇÃO DE ELEMENTOS ===');

    // Verifica input principal (NÃO deve ser alterado)
    const mainInput = document.querySelector('.currency-input[is-main="true"]');
    console.warn(
      'Input principal:',
      mainInput ? '✅ Encontrado (não será alterado)' : '❌ Não encontrado'
    );
    if (mainInput) console.warn('  - Valor atual:', mainInput.value);

    // Verifica prompt input
    console.warn('Prompt input:', this.PromptInput ? '✅ Encontrado' : '❌ Não encontrado');

    // Verifica botão process
    console.warn(
      'Botão process:',
      this.ProcessPromptButton ? '✅ Encontrado' : '❌ Não encontrado'
    );

    // Verifica itens patrimoniais com input-settings="receive"
    const patrimonyItems = document.querySelectorAll('.patrimonio_interactive_item');
    const receiveInputs = document.querySelectorAll('input[input-settings="receive"]');
    console.warn(`Itens patrimoniais: ${patrimonyItems.length} encontrados`);
    console.warn(`Inputs individuais (receive): ${receiveInputs.length} encontrados`);

    // Verifica container wrapper
    const wrapper = document.querySelector('.patrimonio_interactive_content-wrapper');
    console.warn('Container wrapper:', wrapper ? '✅ Encontrado' : '❌ Não encontrado');

    // Verifica sistema AppCalcReino
    console.warn('AppCalcReino:', window.AppCalcReino ? '✅ Disponível' : '❌ Não disponível');
    if (window.AppCalcReino) {
      const patrimonySyncModule = window.AppCalcReino.getModule('patrimonySync');
      console.warn('PatrimonySync:', patrimonySyncModule ? '✅ Conectado' : '❌ Não conectado');
    }

    console.warn('=== FIM DA VERIFICAÇÃO ===');
  }

  // Expõe funções de debug globalmente para desenvolvimento
  exposeDebugFunctions() {
    if (typeof window !== 'undefined' && window.AppCalcReino) {
      window.debugOpenAI = {
        mapping: () => this.debugPatrimonyMapping(),
        test: (term) => this.testCategoryMapping(term),
        items: () => this.getPatrimonyItems(),
        extract: (text) => this.extractAllocationsFromText(text),
        findMatch: (category) => this.findBestMatchingItem(category),
        testValue: (text) => this.testValueExtraction(text),
        testPercentages: (text) => this.testPercentageExtraction(text),
        checkElements: () => this.debugElementsCheck(),
        reconnect: () => this.attemptConnection(),
        forceReconnect: () => {
          this.patrimonySync = null;
          this.getPatrimonySync();
        },
      };
      console.warn('🔧 Debug OpenAI disponível em window.debugOpenAI');
      console.warn('  - debugOpenAI.checkElements() para verificar integração');
      console.warn('  - debugOpenAI.testPercentages("30% em cdb") para testar percentuais');
      console.warn('  - debugOpenAI.testValue("tenho 100 mil") para testar valores');
    }
  }
}
