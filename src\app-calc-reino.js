/**
 * Main Application System
 * Coordinates all modules and handles initialization
 */

// Import all modules
import { ChartAnimationSystem } from './modules/chart-animations.js';
import { CurrencyControlSystem } from './modules/currency-control.js';
import { CurrencyFormattingSystem } from './modules/currency-formatting.js';
import { MotionAnimationSystem } from './modules/motion-animation.js';
import { OpenAIAllocationSystem } from './modules/openai-allocation.js';
import { PatrimonySyncSystem } from './modules/patrimony-sync.js';
import { ProductSystem } from './modules/product-system.js';
import { SectionVisibilitySystem } from './modules/section-visibility.js';

class AppCalculationSystem {
  constructor() {
    this.isInitialized = false;
    this.modules = new Map();
    this.config = {
      enableLogging: true,
      enableAnimations: true,
      enableOpenAI: true,
      enableSync: true,
    };
  }

  async init(customConfig = {}) {
    if (this.isInitialized) {
      // console.warn('Sistema já foi inicializado');
      return;
    }

    // Merge configurações
    this.config = { ...this.config, ...customConfig };

    try {
      // Log de inicialização
      if (this.config.enableLogging) {
        // console.log('🚀 Iniciando App Calc Reino...');
      }

      // Aguarda dependências externas
      await this.waitForDependencies();

      // Inicializa módulos na ordem correta
      await this.initializeModules();

      // Configura comunicação entre módulos
      this.setupModuleCommunication();

      // Configura handlers globais
      this.setupGlobalHandlers();

      this.isInitialized = true;

      if (this.config.enableLogging) {
        // console.log('✅ App Calc Reino inicializado com sucesso!');
        // console.log('📦 Módulos carregados:', Array.from(this.modules.keys()));
      }
    } catch (error) {
      // console.error('❌ Erro na inicialização do App Calc Reino:', error);
      throw error;
    }
  }

  async waitForDependencies() {
    const dependencies = [
      { name: 'Currency', object: () => window.Currency },
      { name: 'Motion', object: () => window.Motion },
      { name: 'gsap', object: () => window.gsap },
    ];

    const promises = dependencies.map((dep) => this.waitForDependency(dep));
    await Promise.all(promises);
  }

  waitForDependency(dependency) {
    return new Promise((resolve) => {
      const check = () => {
        if (dependency.object()) {
          if (this.config.enableLogging) {
            // console.log(`✓ ${dependency.name} carregado`);
          }
          resolve();
        } else {
          setTimeout(check, 100);
        }
      };
      check();
    });
  }

  async initializeModules() {
    // Ordem de inicialização baseada em dependências
    const moduleInitOrder = [
      { name: 'currencyControl', class: CurrencyControlSystem },
      { name: 'currencyFormatting', class: CurrencyFormattingSystem },
      { name: 'motionAnimation', class: MotionAnimationSystem },
      { name: 'patrimonySync', class: PatrimonySyncSystem },
      { name: 'productSystem', class: ProductSystem },
      { name: 'sectionVisibility', class: SectionVisibilitySystem },
      { name: 'chartAnimations', class: ChartAnimationSystem },
      { name: 'openaiAllocation', class: OpenAIAllocationSystem },
    ];

    for (const moduleInfo of moduleInitOrder) {
      try {
        const moduleInstance = new moduleInfo.class();
        this.modules.set(moduleInfo.name, moduleInstance);

        // Inicializa o módulo
        await moduleInstance.init();

        if (this.config.enableLogging) {
          // console.log(`✓ Módulo ${moduleInfo.name} inicializado`);
        }
      } catch (error) {
        // console.error(`❌ Erro ao inicializar módulo ${moduleInfo.name}:`, error);
        // Continua a inicialização mesmo se um módulo falhar
      }
    }
  }

  setupModuleCommunication() {
    // Configura comunicação entre módulos

    // Sincroniza valores entre currency formatting e patrimony sync
    const currencyFormatting = this.modules.get('currencyFormatting');
    const patrimonySync = this.modules.get('patrimonySync');

    if (currencyFormatting && patrimonySync) {
      // Quando o valor principal muda, notifica o patrimony sync
      currencyFormatting.onMainValueChange = (value) => {
        patrimonySync.updateCurrentValue?.(value);
      };
    }

    // Conecta openai allocation com valores do patrimônio
    const openaiAllocation = this.modules.get('openaiAllocation');
    if (openaiAllocation && patrimonySync) {
      // Atualiza valor no OpenAI quando patrimônio muda
      patrimonySync.onTotalChange = (value) => {
        openaiAllocation.updateCurrentValue?.(value);
      };
    }

    // Conecta section visibility com chart animations
    const sectionVisibility = this.modules.get('sectionVisibility');
    const chartAnimations = this.modules.get('chartAnimations');

    if (sectionVisibility && chartAnimations) {
      // Quando seção com gráficos é mostrada, trigger animações
      sectionVisibility.onSectionShow = (sectionName) => {
        if (sectionName === 'resultado' || sectionName === 'alocacao') {
          setTimeout(() => {
            chartAnimations.replayChart?.('pie-0');
          }, 300);
        }
      };
    }
  }

  setupGlobalHandlers() {
    // Handler para erros não capturados
    window.addEventListener('error', (event) => {
      // console.error('Erro global capturado:', event.error);
    });

    // Handler para promessas rejeitadas
    window.addEventListener('unhandledrejection', (event) => {
      // console.error('Promise rejeitada:', event.reason);
    });

    // Handler para mudanças de visibilidade da página
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        // Pausa animações quando página não está visível
        const chartAnimations = this.modules.get('chartAnimations');
        chartAnimations?.pauseAllAnimations?.();
      } else {
        // Resume animações quando página volta a ser visível
        const chartAnimations = this.modules.get('chartAnimations');
        chartAnimations?.resumeAllAnimations?.();
      }
    });

    // Handler para redimensionamento da janela
    let resizeTimeout;
    window.addEventListener('resize', () => {
      clearTimeout(resizeTimeout);
      resizeTimeout = setTimeout(() => {
        this.handleWindowResize();
      }, 250);
    });
  }

  handleWindowResize() {
    // Notifica módulos sobre redimensionamento
    this.modules.forEach((module) => {
      if (typeof module.onWindowResize === 'function') {
        module.onWindowResize();
      }
    });
  }

  // Métodos públicos para controle da aplicação

  getModule(moduleName) {
    return this.modules.get(moduleName);
  }

  enableModule(moduleName) {
    const module = this.modules.get(moduleName);
    if (module && typeof module.enable === 'function') {
      module.enable();
    }
  }

  disableModule(moduleName) {
    const module = this.modules.get(moduleName);
    if (module && typeof module.disable === 'function') {
      module.disable();
    }
  }

  restartModule(moduleName) {
    const module = this.modules.get(moduleName);
    if (module) {
      if (typeof module.destroy === 'function') {
        module.destroy();
      }
      if (typeof module.init === 'function') {
        module.init();
      }
    }
  }

  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };

    // Aplica configurações aos módulos
    this.modules.forEach((module) => {
      if (typeof module.updateConfig === 'function') {
        module.updateConfig(this.config);
      }
    });
  }

  getSystemStatus() {
    const status = {
      initialized: this.isInitialized,
      modules: {},
      config: this.config,
    };

    this.modules.forEach((module, name) => {
      status.modules[name] = {
        loaded: true,
        initialized: module.isInitialized || false,
      };
    });

    return status;
  }

  destroy() {
    // Destrói todos os módulos
    this.modules.forEach((module) => {
      if (typeof module.destroy === 'function') {
        module.destroy();
      }
    });

    this.modules.clear();
    this.isInitialized = false;

    if (this.config.enableLogging) {
      // console.log('🔄 App Calc Reino destruído');
    }
  }
}

// Instância global
const AppCalcReino = new AppCalculationSystem();

// Auto-inicialização quando DOM estiver pronto
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    AppCalcReino.init().catch(() => {});
  });
} else {
  // DOM já está pronto
  AppCalcReino.init().catch(() => {});
}

// Exporta para uso global
window.AppCalcReino = AppCalcReino;

export { AppCalcReino, AppCalculationSystem };
export default AppCalcReino;
