# Sistema de Linguagem Natural - OpenAI Integration

## 🎯 Visão Geral

O sistema OpenAI suporta entrada completamente natural, permitindo que usuários escrevam prompts informais para alocar seu patrimônio. O sistema NÃO altera o input principal (onde o usuário define seu patrimônio total), apenas os inputs individuais dentro dos `patrimonio_interactive_item`.

## 🗣️ Linguagem Natural Suportada

### Exemplos de Prompts Aceitos

#### Valores Pequenos
```
"tenho 20 reais em cdb"
"possuo 50 reais, como investir?"
"com 100 reais quero começar a investir"
"meus 200 reais onde aplicar?"
```

#### Valores Médios
```
"tenho 5 mil pra investir em renda fixa"
"possuo 10 mil, quero 70% cdb e 30% ações"
"com 50 mil, dividir entre fundos e tesouro"
"valor de 100 mil para diversificar"
```

#### Valores Grandes
```
"patrimônio de 500 mil para alocar"
"tenho 1 milhão, estratégia conservadora"
"com 2 milhões, perfil arrojado"
```

#### Linguagem Informal e Percentuais
```
"tenho uma graninha pra investir, uns 300 reais"
"sobrou uma grana, tipo 5 mil, onde boto?"
"quero 30% em cdb e 70% em ações"
"coloco 50% em renda fixa"
"tenho 20% em cdi, resto em tesouro"
```

## 💰 Extração de Valores

### Padrões Reconhecidos

#### Formato Direto
- `"tenho R$ 1.000"`
- `"possuo 5.000 reais"`
- `"valor de 10 mil"`
- `"com 50.000"`

#### Com Multiplicadores
- `"20 mil"` → R$ 20.000
- `"5 mil reais"` → R$ 5.000
- `"100k"` → R$ 100.000
- `"1 milhão"` → R$ 1.000.000

#### Contextuais
- `"tenho disponível 500"`
- `"meu patrimônio é 10 mil"`
- `"total de 25.000"`
- `"disponho de 75 mil"`

### Heurísticas Inteligentes

```javascript
// Valores de 1-3 dígitos são interpretados como milhares
"tenho 100" → R$ 100.000 (não R$ 100)
"possuo 50" → R$ 50.000

// Valores muito pequenos são mantidos
"tenho 20 reais" → R$ 20
"sobrou 50" → R$ 50

// Valores com decimais são respeitados
"tenho 1.500,50" → R$ 1.500,50
"possuo 10.000,00" → R$ 10.000,00
```

## 🔧 Configurações do Modelo

### Parâmetros Otimizados

```javascript
{
  model: 'gpt-4',
  max_tokens: 1500,        // Mais tokens para respostas detalhadas
  temperature: 0.8,        // Maior criatividade para linguagem natural
  top_p: 0.9,             // Diversidade nas respostas
  frequency_penalty: 0.2,  // Evita repetições
  presence_penalty: 0.1    // Encoraja novos tópicos
}
```

### System Prompt Adaptado

```
Você é um consultor financeiro especializado em alocação de ativos no mercado brasileiro. 

CARACTERÍSTICAS IMPORTANTES:
- Seja muito flexível com linguagem natural, informal e coloquial
- Interprete a intenção do usuário mesmo com erros de gramática, gírias ou valores pequenos
- Sempre forneça sugestões construtivas independente do valor disponível
- Para valores pequenos, foque em produtos acessíveis
- Para valores muito pequenos, sugira começar gradualmente

ADAPTAÇÃO POR VALOR:
- Menos de R$ 100: Poupança, CDB simples, educação financeira
- R$ 100-1.000: CDB, fundos com aplicação mínima baixa
- R$ 1.000-10.000: Diversificação básica, tesouro direto
- Mais de R$ 10.000: Estratégias completas de diversificação
```

## 🧪 Testes e Debug

### Comandos de Teste

```javascript
// Testar extração de valores
debugOpenAI.testValue("tenho 20 reais em cdb")
debugOpenAI.testValue("possuo 50 mil para investir")
debugOpenAI.testValue("com 100k quero diversificar")

// Testar correspondência de categorias
debugOpenAI.test("cdb")
debugOpenAI.test("renda fixa")
debugOpenAI.test("ações")

// Ver mapeamento completo
debugOpenAI.mapping()
```

### Exemplos de Output

```
=== TESTE DE EXTRAÇÃO DE VALOR: "tenho 20 reais em cdb" ===
VALOR EXTRAÍDO: R$ 20
VALOR FORMATADO: R$ 20,00
=== FIM DO TESTE ===

=== TESTE DE EXTRAÇÃO DE VALOR: "possuo 50 mil" ===
VALOR EXTRAÍDO: R$ 50000
VALOR FORMATADO: R$ 50.000,00
=== FIM DO TESTE ===
```

## 📈 Fluxo Completo

### Processo de Interpretação

1. **Entrada do usuário**: `"quero 30% em cdb"`

2. **Extração de percentual**: 
   - Identifica "30% em cdb" → 30% para item CDB
   - NÃO altera input principal (usuário já definiu seu patrimônio)

3. **Verificação de valor**:
   - Se input principal vazio, extrai valor do texto
   - Se input principal preenchido, usa esse valor
   - Calcula valores absolutos baseado no patrimônio total

4. **Aplicação direta**:
   - Aplica 30% no input CDB (`input-settings="receive"`)
   - Atualiza slider correspondente
   - Mantém input principal intocado

5. **Para casos sem percentual específico**: 
   - Envia para IA para sugestão completa
   - Aplica resultados nos inputs individuais

## 🎨 Adaptações por Faixa de Valor

### Valores Pequenos (< R$ 100)
- **Foco**: Educação e primeiros passos
- **Produtos**: CDB, poupança
- **Tom**: Encorajador, educativo

### Valores Baixos (R$ 100 - R$ 1.000)
- **Foco**: Construção de hábito
- **Produtos**: CDB, fundos simples
- **Tom**: Motivacional, prático

### Valores Médios (R$ 1.000 - R$ 50.000)
- **Foco**: Diversificação básica
- **Produtos**: CDB, Tesouro, fundos
- **Tom**: Profissional, didático

### Valores Altos (> R$ 50.000)
- **Foco**: Estratégia completa
- **Produtos**: Todas as categorias
- **Tom**: Sofisticado, detalhado

## 💡 Exemplos Práticos

### Caso 1: Percentual Específico
```
Input: "quero 30% em cdb"
Input principal: R$ 10.000,00 (já definido pelo usuário)

Processamento:
- Detecta percentual direto: 30% → CDB
- Usa valor do input principal: R$ 10.000,00
- Calcula: 30% de R$ 10.000 = R$ 3.000,00

Output direto:
- Input CDB: R$ 3.000,00
- Slider CDB: 30%
- Input principal: INALTERADO
```

### Caso 2: Sem Input Principal
```
Input: "tenho 50 reais, tudo em cdb"

Processamento:
- Extrai valor: R$ 50,00
- Identifica intenção: 100% CDB
- Input principal vazio

Output:
- Usa R$ 50 para cálculos
- Input CDB: R$ 50,00
- Slider CDB: 100%
- Input principal: PERMANECE VAZIO
```

### Caso 3: Múltiplos Percentuais
```
Input: "30% cdb, 40% ações, 30% tesouro"
Input principal: R$ 100.000,00

Processamento:
- Extrai 3 percentuais específicos
- Aplica diretamente nos inputs
- Não precisa consultar IA

Output:
- CDB: R$ 30.000 (30%)
- Ações: R$ 40.000 (40%) 
- Títulos Públicos: R$ 30.000 (30%)
```

## ⚡ Performance e Otimizações

### Estratégias de Cache
- Cache de respostas similares
- Reutilização para valores próximos
- Templates para faixas de valor

### Validações Inteligentes
- Verificação de coerência
- Limites mínimos por produto
- Adequação perfil vs valor

### Fallbacks
- Sistema legado para casos edge
- Valores padrão para extrações falhas
- Mensagens educativas para erros

---

*Sistema projetado para democratizar o acesso a consultoria financeira através de linguagem natural acessível.*